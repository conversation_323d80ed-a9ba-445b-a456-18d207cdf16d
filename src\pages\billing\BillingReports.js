import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Card, Form, Button, Row, Col, Table, InputGroup, Alert,
  Badge, Spinner, Modal, OverlayTrigger, Tooltip, Dropdown
} from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSearch, faDownload, faEye, faFileInvoiceDollar,
  faCalendarAlt, faUser, faPhone, faBuilding, faSpinner,
  faExclamationTriangle, faCheckCircle, faInfoCircle, faFilter
} from '@fortawesome/free-solid-svg-icons';
import billingReportsAPI from '../../services/billingReportsAPI';
import { useAuth } from '../../context/AuthContext';
import { useTenant } from '../../context/TenantContext';
import PaginatedTestCards from '../../components/billing/PaginatedTestCards';
import Pagination from '../../components/common/Pagination';
import '../../styles/BillingReports.css';
import '../../styles/TestDetailsCard.css';

const BillingReports = () => {
  // const location = useLocation();
  const navigate = useNavigate();

  const { user } = useAuth();
  const { accessibleTenants, currentTenantContext } = useTenant();

  // State management
  const [reports, setReports] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const [error, setError] = useState('');
  const [stats, setStats] = useState(null);

  // Franchise filtering state
  const [selectedFranchiseId, setSelectedFranchiseId] = useState(null);
  const [allReports, setAllReports] = useState([]); // Store all reports for filtering

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Debug logging
  console.log('BillingReports render - reports:', reports.length, 'loading:', loading);

  // Search state
  const [searchParams, setSearchParams] = useState({
    sid: '',
    patient_name: '',
    mobile: '',
    date_from: '',
    date_to: ''
  });

  // SID autocomplete state
  const [sidSuggestions, setSidSuggestions] = useState([]);
  const [showSidSuggestions, setShowSidSuggestions] = useState(false);

  // Modal state
  const [showReportModal, setShowReportModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState(null);
  const [reportModalLoading, setReportModalLoading] = useState(false);
  const [downloadingPDF, setDownloadingPDF] = useState(false);

  // Helper function to check if user can access all franchises
  const canAccessAllFranchises = () => {
    // Admin role can access all franchises
    if (user?.role === 'admin') return true;

    // Users from Mayiladuthurai hub (tenant_id 1) can access all franchises
    if (currentTenantContext?.is_hub && currentTenantContext?.site_code === 'MYD') return true;

    return false;
  };

  // Helper function to get available franchises for filtering
  const getAvailableFranchises = () => {
    if (!canAccessAllFranchises()) return [];
    return accessibleTenants || [];
  };

  // Helper function to filter reports by franchise
  const filterReportsByFranchise = (reportsData) => {
    if (!canAccessAllFranchises() || !selectedFranchiseId) {
      return reportsData;
    }
    return reportsData.filter(report => report.tenant_id === selectedFranchiseId);
  };

  // Load initial data
  useEffect(() => {
    loadReports();
    loadStats();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Reload data when franchise selection changes
  useEffect(() => {
    if (canAccessAllFranchises()) {
      loadReports();
      loadStats();
    }
  }, [selectedFranchiseId]); // eslint-disable-line react-hooks/exhaustive-deps

  const loadReports = async () => {
    setLoading(true);
    setError('');

    try {
      console.log('[BillingReports] Starting to load all reports...');
      console.log('[BillingReports] Current user:', user);
      console.log('[BillingReports] Selected franchise:', selectedFranchiseId);

      // Use getAllReports to get all reports with role-based filtering
      const response = await billingReportsAPI.getAllReports(selectedFranchiseId);

      if (response.success) {
        // Extract reports data from the nested response structure
        let reportsData = response.data?.data?.data || response.data?.data || [];

        // Ensure we have an array of reports
        if (Array.isArray(reportsData)) {
          console.log('[BillingReports] Loaded reports:', reportsData.length);

          // Store all reports for filtering
          setAllReports(reportsData);

          // Apply franchise filtering if applicable
          const filteredReports = filterReportsByFranchise(reportsData);
          setReports(filteredReports);

          // Reset to first page when data changes
          setCurrentPage(1);
        } else {
          console.error('Reports data is not an array:', reportsData);
          setReports([]);
          setAllReports([]);
        }
      } else {
        setError(response.error || 'Failed to load reports');
        setReports([]);
        setAllReports([]);
      }
    } catch (err) {
      console.error('[BillingReports] Load reports error:', err);
      console.error('[BillingReports] Error details:', err.response?.data);
      setError('Failed to load billing reports: ' + (err.response?.data?.message || err.message));
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    try {
      console.log('[BillingReports] Loading stats for franchise:', selectedFranchiseId);
      const response = await billingReportsAPI.getReportsStats(selectedFranchiseId);
      console.log('[BillingReports] Stats response:', response);

      if (response.success) {
        // The API returns: { success: true, data: { data: { data: actualStats } } }
        const statsData = response.data?.data?.data || response.data?.data || response.data;
        console.log('[BillingReports] Extracted stats data:', statsData);
        setStats(statsData || {});
      } else {
        console.error('[BillingReports] Stats API error:', response.error);
      }
    } catch (err) {
      console.error('Failed to load stats:', err);
    }
  };

  const handleSearch = async () => {
    setSearchLoading(true);
    setError('');

    try {
      const response = await billingReportsAPI.searchReports(searchParams, selectedFranchiseId);
      if (response.success) {
        let reportsData = response.data?.data?.data || response.data?.data || [];
        console.log('[BillingReports] Search results:', reportsData.length);

        if (Array.isArray(reportsData)) {
          // Store all search results
          setAllReports(reportsData);

          // Apply franchise filtering if applicable
          const filteredReports = filterReportsByFranchise(reportsData);
          setReports(filteredReports);

          // Reset to first page when search results change
          setCurrentPage(1);
        } else {
          setReports([]);
          setAllReports([]);
        }
      } else {
        setError(response.error);
      }
    } catch (err) {
      console.error('[BillingReports] Search error:', err);
      setError('Failed to search billing reports');
    } finally {
      setSearchLoading(false);
    }
  };

  const handleSIDChange = async (value) => {
    setSearchParams(prev => ({ ...prev, sid: value }));
    
    if (value.length >= 2) {
      try {
        const response = await billingReportsAPI.getSIDAutocomplete(value);
        if (response.success) {
          const suggestionsData = response.data?.data || response.data || [];
          setSidSuggestions(Array.isArray(suggestionsData) ? suggestionsData : []);
          setShowSidSuggestions(true);
        }
      } catch (err) {
        console.error('Failed to get SID suggestions:', err);
      }
    } else {
      setSidSuggestions([]);
      setShowSidSuggestions(false);
    }
  };

  const handleSIDSelect = (sid) => {
    setSearchParams(prev => ({ ...prev, sid }));
    setShowSidSuggestions(false);
    setSidSuggestions([]);
  };

  // Navigate to billing reports detail page
  const handleNavigateToDetail = (report, editMode = false) => {
    const params = new URLSearchParams();
    if (editMode) {
      params.append('edit', 'true');
    }

    const queryString = params.toString();
    const url = `/billing/reports/${report.sid_number}${queryString ? `?${queryString}` : ''}`;

    navigate(url, {
      state: { from: 'billing-reports' }
    });
  };

  const handleViewReport = async (report) => {
    setSelectedReport(report);
    setShowReportModal(true);
    setReportModalLoading(true);

    try {
      const response = await billingReportsAPI.getReportBySID(report.sid_number);

      if (response.success && response.data) {
        // The API returns: { success: true, data: { data: { data: actualReport } } }
        // So we need to access response.data.data.data
        const reportData = response.data.data?.data || response.data.data || response.data;

        if (reportData && typeof reportData === 'object') {
          // Force a state update by creating a new object
          setSelectedReport({ ...reportData });
        } else {
          setError('Invalid report data structure received');
        }
      } else {
        setError(response.error || 'Failed to load report details');
      }
    } catch (err) {
      console.error('Error loading report:', err);
      setError('Failed to load report details');
    } finally {
      setReportModalLoading(false);
    }
  };

  const handleDownloadPDF = async (report) => {
    try {
      setDownloadingPDF(true);
      setError(null);

      const response = await billingReportsAPI.downloadReportPDF(report.id);
      if (response.success) {
        billingReportsAPI.downloadPDFBlob(
          response.data,
          `billing_report_${report.sid_number}.pdf`
        );

        // Show success message
        console.log(`PDF downloaded successfully: billing_report_${report.sid_number}.pdf`);
      } else {
        setError(response.error || 'Failed to download PDF');
      }
    } catch (err) {
      console.error('Error downloading PDF:', err);
      setError('Failed to download PDF. Please try again.');
    } finally {
      setDownloadingPDF(false);
    }
  };

  const clearSearch = () => {
    setSearchParams({
      sid: '',
      patient_name: '',
      mobile: '',
      date_from: '',
      date_to: ''
    });
    loadReports();
  };

  // Pagination calculations
  const totalItems = reports.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedReports = reports.slice(startIndex, endIndex);

  const handlePageChange = (page) => {
    setCurrentPage(page);
    // Scroll to top of table on page change
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="billing-reports-container">
      {/* Header */}
      <div className="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 className="h3 mb-0 text-gray-800">
          <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
          Billing Reports
        </h1>
        <div className="d-flex gap-2">
          <Button 
            variant="outline-primary" 
            onClick={loadReports}
            disabled={loading}
          >
            <FontAwesomeIcon icon={faSpinner} spin={loading} className="me-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      {stats && (
        <Row className="mb-4">
          {/* Debug: Log stats object */}
          {console.log('[BillingReports] Rendering stats:', stats)}
          <Col md={3}>
            <Card className="border-left-primary shadow h-100 py-2">
              <Card.Body>
                <Row className="no-gutters align-items-center">
                  <Col className="mr-2">
                    <div className="text-xs font-weight-bold text-primary text-uppercase mb-1">
                      Total Reports
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">
                      {stats.total_reports || 0}
                    </div>
                  </Col>
                  <Col xs="auto">
                    <FontAwesomeIcon icon={faFileInvoiceDollar} className="fa-2x text-gray-300" />
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="border-left-success shadow h-100 py-2">
              <Card.Body>
                <Row className="no-gutters align-items-center">
                  <Col className="mr-2">
                    <div className="text-xs font-weight-bold text-success text-uppercase mb-1">
                      Total Amount
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">
                      {billingReportsAPI.formatCurrency(stats.total_amount || 0)}
                    </div>
                  </Col>
                  <Col xs="auto">
                    <FontAwesomeIcon icon={faCheckCircle} className="fa-2x text-gray-300" />
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="border-left-info shadow h-100 py-2">
              <Card.Body>
                <Row className="no-gutters align-items-center">
                  <Col className="mr-2">
                    <div className="text-xs font-weight-bold text-info text-uppercase mb-1">
                      Recent Reports (7 days)
                    </div>
                    <div className="h5 mb-0 font-weight-bold text-gray-800">
                      {stats.recent_reports_count || 0}
                    </div>
                  </Col>
                  <Col xs="auto">
                    <FontAwesomeIcon icon={faCalendarAlt} className="fa-2x text-gray-300" />
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
          <Col md={3}>
            <Card className="border-left-warning shadow h-100 py-2">
              <Card.Body>
                <Row className="no-gutters align-items-center">
                  <Col className="mr-2">
                    <div className="text-xs font-weight-bold text-warning text-uppercase mb-1">
                      Access Level
                    </div>
                    <div className="h6 mb-0 font-weight-bold text-gray-800">
                      {stats.user_access_level === 'all_franchises' ? 'All Franchises' : 'Own Franchise'}
                    </div>
                  </Col>
                  <Col xs="auto">
                    <FontAwesomeIcon icon={faBuilding} className="fa-2x text-gray-300" />
                  </Col>
                </Row>
              </Card.Body>
            </Card>
          </Col>
        </Row>
      )}

      {/* Search Section */}
      <Card className="shadow mb-4">
        <Card.Header className="py-3">
          <div className="d-flex justify-content-between align-items-center">
            <h6 className="m-0 font-weight-bold text-primary">Search Billing Reports</h6>
            {canAccessAllFranchises() && (
              <Dropdown>
                <Dropdown.Toggle variant="outline-primary" size="sm">
                  <FontAwesomeIcon icon={faFilter} className="me-2" />
                  {selectedFranchiseId
                    ? getAvailableFranchises().find(f => f.id === selectedFranchiseId)?.name || 'Select Franchise'
                    : 'All Franchises'
                  }
                </Dropdown.Toggle>
                <Dropdown.Menu>
                  <Dropdown.Item
                    onClick={() => setSelectedFranchiseId(null)}
                    className={!selectedFranchiseId ? 'active' : ''}
                  >
                    <FontAwesomeIcon icon={faBuilding} className="me-2" />
                    All Franchises
                  </Dropdown.Item>
                  <Dropdown.Divider />
                  {getAvailableFranchises().map((franchise) => (
                    <Dropdown.Item
                      key={franchise.id}
                      onClick={() => setSelectedFranchiseId(franchise.id)}
                      className={selectedFranchiseId === franchise.id ? 'active' : ''}
                    >
                      <FontAwesomeIcon icon={faBuilding} className="me-2" />
                      {franchise.name}
                      <small className="text-muted d-block">{franchise.site_code}</small>
                    </Dropdown.Item>
                  ))}
                </Dropdown.Menu>
              </Dropdown>
            )}
          </div>
        </Card.Header>
        <Card.Body>
          <Row>
            <Col md={6}>
              <Form.Group className="mb-3 position-relative">
                <Form.Label>SID Number</Form.Label>
                <InputGroup>
                  <Form.Control
                    type="text"
                    placeholder="Enter SID number (e.g., MYD001, SKZ001, TNJ001)"
                    value={searchParams.sid}
                    onChange={(e) => handleSIDChange(e.target.value)}
                    onFocus={() => setShowSidSuggestions(sidSuggestions.length > 0)}
                  />
                  <Button variant="outline-secondary" onClick={handleSearch} disabled={searchLoading}>
                    <FontAwesomeIcon icon={faSearch} />
                  </Button>
                </InputGroup>
                
                {/* SID Autocomplete Dropdown */}
                {showSidSuggestions && sidSuggestions.length > 0 && (
                  <div className="sid-suggestions-dropdown">
                    {sidSuggestions.map((sid, index) => (
                      <div
                        key={index}
                        className="sid-suggestion-item"
                        onClick={() => handleSIDSelect(sid)}
                      >
                        {sid}
                      </div>
                    ))}
                  </div>
                )}
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Patient Name</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter patient name"
                  value={searchParams.patient_name}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, patient_name: e.target.value }))}
                />
              </Form.Group>
            </Col>
          </Row>
          
          <Row>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>Mobile Number</Form.Label>
                <Form.Control
                  type="text"
                  placeholder="Enter mobile number"
                  value={searchParams.mobile}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, mobile: e.target.value }))}
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>From Date</Form.Label>
                <Form.Control
                  type="date"
                  value={searchParams.date_from}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, date_from: e.target.value }))}
                />
              </Form.Group>
            </Col>
            <Col md={4}>
              <Form.Group className="mb-3">
                <Form.Label>To Date</Form.Label>
                <Form.Control
                  type="date"
                  value={searchParams.date_to}
                  onChange={(e) => setSearchParams(prev => ({ ...prev, date_to: e.target.value }))}
                />
              </Form.Group>
            </Col>
          </Row>
          
          <div className="d-flex gap-2">
            <Button 
              variant="primary" 
              onClick={handleSearch}
              disabled={searchLoading}
            >
              <FontAwesomeIcon icon={searchLoading ? faSpinner : faSearch} spin={searchLoading} className="me-2" />
              Search Reports
            </Button>
            <Button variant="outline-secondary" onClick={clearSearch}>
              Clear
            </Button>
          </div>
        </Card.Body>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert variant="danger" dismissible onClose={() => setError('')}>
          <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
          {error}
        </Alert>
      )}

      {/* Reports Table */}
      <Card className="shadow">
        <Card.Header className="py-3">
          <div className="d-flex justify-content-between align-items-center">
            <h6 className="m-0 font-weight-bold text-primary">
              Billing Reports ({reports.length})
            </h6>
            {reports.length > 0 && (
              <small className="text-muted">
                Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} reports
              </small>
            )}
          </div>
        </Card.Header>
        <Card.Body>
          {loading ? (
            <div className="text-center py-4">
              <Spinner animation="border" variant="primary" />
              <p className="mt-2">Loading billing reports...</p>
            </div>
          ) : reports.length === 0 ? (
            <div className="text-center py-4">
              <FontAwesomeIcon icon={faInfoCircle} size="3x" className="text-muted mb-3" />
              <h5 className="text-muted">No billing reports found</h5>
              <p className="text-muted">Try adjusting your search criteria or create a new billing record.</p>
            </div>
          ) : (
            <>
              <div className="table-responsive">
                <Table hover>
                  <thead>
                    <tr>
                      <th>SID Number</th>
                      <th>Patient</th>
                      <th>Clinic</th>
                      <th>Billing Date</th>
                      <th>Tests</th>
                      <th>Amount</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {Array.isArray(paginatedReports) ? paginatedReports.map((report) => (
                    <tr key={report.id}>
                      <td>
                        <strong className="text-primary">{report.sid_number}</strong>
                      </td>
                      <td>
                        <div>
                          <FontAwesomeIcon icon={faUser} className="me-1 text-muted" />
                          {report.patient_name}
                        </div>
                        {report.patient_mobile && (
                          <small className="text-muted">
                            <FontAwesomeIcon icon={faPhone} className="me-1" />
                            {report.patient_mobile}
                          </small>
                        )}
                      </td>
                      <td>
                        <FontAwesomeIcon icon={faBuilding} className="me-1 text-muted" />
                        {report.clinic_name}
                      </td>
                      <td>
                        <FontAwesomeIcon icon={faCalendarAlt} className="me-1 text-muted" />
                        {billingReportsAPI.formatDate(report.billing_date)}
                      </td>
                      <td>
                        <Badge bg="info">{report.test_count} tests</Badge>
                      </td>
                      <td>
                        <strong>{billingReportsAPI.formatCurrency(report.total_amount)}</strong>
                      </td>
                      <td>
                        <Badge bg={billingReportsAPI.getStatusVariant(report.status)}>
                          {report.status}
                        </Badge>
                      </td>
                      <td>
                        <div className="d-flex gap-1">
                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip>View Report Details</Tooltip>}
                          >
                            <Button
                              variant="outline-primary"
                              size="sm"
                              onClick={() => handleViewReport(report)}
                            >
                              <FontAwesomeIcon icon={faEye} />
                            </Button>
                          </OverlayTrigger>

                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip>Download PDF Report</Tooltip>}
                          >
                            <Button
                              variant="outline-success"
                              size="sm"
                              onClick={() => handleDownloadPDF(report)}
                              className="ms-1"
                              disabled={downloadingPDF}
                            >
                              {downloadingPDF ? (
                                <FontAwesomeIcon icon={faSpinner} spin />
                              ) : (
                                <FontAwesomeIcon icon={faDownload} />
                              )}
                            </Button>
                          </OverlayTrigger>

                          <OverlayTrigger
                            placement="top"
                            overlay={<Tooltip>Download PDF</Tooltip>}
                          >
                            <Button
                              variant="outline-success"
                              size="sm"
                              onClick={() => handleDownloadPDF(report)}
                            >
                              <FontAwesomeIcon icon={faDownload} />
                            </Button>
                          </OverlayTrigger>
                        </div>
                      </td>
                    </tr>
                  )) : (
                    <tr>
                      <td colSpan="8" className="text-center text-danger">
                        Error: Reports data is not an array (type: {typeof paginatedReports})
                      </td>
                    </tr>
                  )}
                </tbody>
              </Table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="mt-4">
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  totalItems={totalItems}
                  itemsPerPage={itemsPerPage}
                  onPageChange={handlePageChange}
                  showInfo={false}
                  size="sm"
                />
              </div>
            )}
          </>
          )}
        </Card.Body>
      </Card>

      {/* Report Details Modal */}
      <Modal show={showReportModal} onHide={() => setShowReportModal(false)} size="xl" scrollable>
        <Modal.Header closeButton>
          <Modal.Title>
            <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
            Billing Report Details
            {selectedReport && (
              <Badge bg="primary" className="ms-2">{selectedReport.sid_number}</Badge>
            )}
          </Modal.Title>
          {selectedReport && (
            <Button
              variant="success"
              size="sm"
              onClick={() => handleDownloadPDF(selectedReport)}
              className="ms-auto"
              disabled={downloadingPDF}
            >
              {downloadingPDF ? (
                <>
                  <FontAwesomeIcon icon={faSpinner} spin className="me-1" />
                  Downloading...
                </>
              ) : (
                <>
                  <FontAwesomeIcon icon={faDownload} className="me-1" />
                  Download PDF
                </>
              )}
            </Button>
          )}
        </Modal.Header>
        <Modal.Body>
          {reportModalLoading ? (
            <div className="text-center py-4">
              <Spinner animation="border" variant="primary" />
              <p className="mt-2">Loading report details...</p>
            </div>
          ) : selectedReport ? (
            <div>
              {/* Report Header */}
              <Row className="mb-4">
                <Col md={6}>
                  <Card className="border-left-primary h-100">
                    <Card.Body>
                      <h6 className="text-primary mb-2">Report Information</h6>
                      <p className="mb-1"><strong>SID Number:</strong> {selectedReport.sid_number}</p>
                      <p className="mb-1"><strong>Billing Date:</strong> {billingReportsAPI.formatDate(selectedReport.billing_date)}</p>
                      <p className="mb-1"><strong>Generated:</strong> {billingReportsAPI.formatDateTime(selectedReport.generation_timestamp)}</p>
                      <p className="mb-0"><strong>Status:</strong> <Badge bg={billingReportsAPI.getStatusVariant(selectedReport.metadata?.status)}>{selectedReport.metadata?.status}</Badge></p>
                    </Card.Body>
                  </Card>
                </Col>
                <Col md={6}>
                  <Card className="border-left-info h-100">
                    <Card.Body>
                      <h6 className="text-info mb-2">Clinic Information</h6>
                      <p className="mb-1"><strong>Clinic:</strong> {selectedReport.clinic_info?.name}</p>
                      <p className="mb-1"><strong>Site Code:</strong> {selectedReport.clinic_info?.site_code}</p>
                      <p className="mb-1"><strong>Contact:</strong> {selectedReport.clinic_info?.contact_phone}</p>
                      <p className="mb-0"><strong>Email:</strong> {selectedReport.clinic_info?.email}</p>
                    </Card.Body>
                  </Card>
                </Col>
              </Row>

              {/* Patient Information */}
              <h6 className="text-primary mb-3">
                <FontAwesomeIcon icon={faUser} className="me-2" />
                Patient Information
              </h6>
              <Row className="mb-3">
                <Col md={6}>
                  <p className="mb-1"><strong>Name:</strong> {selectedReport.patient_info?.full_name}</p>
                  <p className="mb-1"><strong>Patient ID:</strong> {selectedReport.patient_info?.patient_id}</p>
                  <p className="mb-1"><strong>Date of Birth:</strong> {billingReportsAPI.formatDate(selectedReport.patient_info?.date_of_birth)}</p>
                </Col>
                <Col md={6}>
                  <p className="mb-1"><strong>Age/Gender:</strong> {selectedReport.patient_info?.age} / {selectedReport.patient_info?.gender}</p>
                  <p className="mb-1"><strong>Blood Group:</strong> {selectedReport.patient_info?.blood_group || 'N/A'}</p>
                  <p className="mb-1"><strong>Mobile:</strong> {selectedReport.patient_info?.mobile}</p>
                </Col>
              </Row>
              {selectedReport.patient_info?.email && (
                <Row className="mb-3">
                  <Col md={12}>
                    <p className="mb-1"><strong>Email:</strong> {selectedReport.patient_info?.email}</p>
                  </Col>
                </Row>
              )}

              {/* Billing Header */}
              {selectedReport.billing_header && (
                <>
                  <h6 className="text-primary mb-3">
                    <FontAwesomeIcon icon={faFileInvoiceDollar} className="me-2" />
                    Billing Information
                  </h6>
                  <Row className="mb-3">
                    <Col md={6}>
                      <p className="mb-1"><strong>Invoice Number:</strong> {selectedReport.billing_header.invoice_number}</p>
                      <p className="mb-1"><strong>Referring Doctor:</strong> {selectedReport.billing_header.referring_doctor}</p>
                    </Col>
                    <Col md={6}>
                      <p className="mb-1"><strong>Payment Status:</strong> {selectedReport.billing_header.payment_status}</p>
                      <p className="mb-1"><strong>Payment Method:</strong> {selectedReport.billing_header.payment_method}</p>
                    </Col>
                  </Row>
                </>
              )}

              {/* Test Items - Enhanced Card Layout */}
              <PaginatedTestCards
                testItems={selectedReport.test_items || []}
                title="Test Details"
                itemsPerPage={5}
              />

              {/* Unmatched Tests Warning */}
              {selectedReport.unmatched_tests && selectedReport.unmatched_tests.length > 0 && (
                <Alert variant="warning" className="mb-3">
                  <FontAwesomeIcon icon={faExclamationTriangle} className="me-2" />
                  <strong>Unmatched Tests ({selectedReport.unmatched_tests.length}):</strong>
                  <ul className="mb-0 mt-2">
                    {selectedReport.unmatched_tests.map((test, index) => (
                      <li key={index}>{test}</li>
                    ))}
                  </ul>
                </Alert>
              )}

              {/* Financial Summary */}
              <h6 className="text-primary mb-3">
                <FontAwesomeIcon icon={faCheckCircle} className="me-2" />
                Financial Summary
              </h6>
              {selectedReport.financial_summary && (
                <Row>
                  <Col md={6}>
                    <p className="mb-1"><strong>Bill Amount:</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.bill_amount)}</p>
                    <p className="mb-1"><strong>Other Charges:</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.other_charges)}</p>
                    <p className="mb-1"><strong>Discount ({selectedReport.financial_summary.discount_percent}%):</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.discount_amount)}</p>
                    <p className="mb-1"><strong>Subtotal:</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.subtotal)}</p>
                  </Col>
                  <Col md={6}>
                    <p className="mb-1"><strong>GST ({selectedReport.financial_summary.gst_rate}%):</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.gst_amount)}</p>
                    <p className="mb-1"><strong>Total Amount:</strong> <span className="text-success fw-bold">{billingReportsAPI.formatCurrency(selectedReport.financial_summary.total_amount)}</span></p>
                    <p className="mb-1"><strong>Paid Amount:</strong> {billingReportsAPI.formatCurrency(selectedReport.financial_summary.paid_amount)}</p>
                    <p className="mb-0"><strong>Balance:</strong> <span className={selectedReport.financial_summary.balance > 0 ? 'text-danger fw-bold' : 'text-success'}>{billingReportsAPI.formatCurrency(selectedReport.financial_summary.balance)}</span></p>
                  </Col>
                </Row>
              )}

              {/* Report Metadata */}
              {selectedReport.metadata && (
                <>
                  <h6 className="text-primary mb-3 mt-4">Report Metadata</h6>
                  <Row>
                    <Col md={6}>
                      <p className="mb-1"><strong>Test Match Rate:</strong>
                        <span className={billingReportsAPI.getMatchRateColor(selectedReport.metadata.test_match_success_rate)}>
                          {' '}{Math.round(selectedReport.metadata.test_match_success_rate * 100)}%
                        </span>
                      </p>
                      <p className="mb-1"><strong>Total Tests:</strong> {selectedReport.metadata.total_tests}</p>
                    </Col>
                    <Col md={6}>
                      <p className="mb-1"><strong>Matched Tests:</strong> {selectedReport.metadata.matched_tests_count}</p>
                      <p className="mb-1"><strong>Unmatched Tests:</strong> {selectedReport.metadata.unmatched_tests_count}</p>
                    </Col>
                  </Row>
                </>
              )}
            </div>
          ) : (
            <Alert variant="warning">No report details available</Alert>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowReportModal(false)}>
            Close
          </Button>
          {selectedReport && (
            <Button
              variant="primary"
              onClick={() => handleDownloadPDF(selectedReport)}
            >
              <FontAwesomeIcon icon={faDownload} className="me-2" />
              Download PDF
            </Button>
          )}
        </Modal.Footer>
      </Modal>
    </div>
  );
};

export default BillingReports;
