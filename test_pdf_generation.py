#!/usr/bin/env python3
"""
Test PDF Generation - Verify that the new PDF generation works correctly
"""

import sys
import os
import requests

def test_pdf_generation():
    """Test PDF generation for billing reports"""
    
    print("🧪 Testing PDF Generation for Billing Reports")
    print("=" * 60)
    
    # Test with report ID 1 (TNJ004)
    report_id = 1
    base_url = "http://localhost:5001"
    
    # First, let's test without authentication to see the error
    print(f"Testing PDF generation for report ID: {report_id}")
    
    try:
        # Make request to PDF endpoint
        url = f"{base_url}/api/billing-reports/{report_id}/pdf"
        print(f"Making request to: {url}")
        
        response = requests.get(url)
        
        print(f"Response status code: {response.status_code}")
        print(f"Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            # Check if we got PDF content
            content_type = response.headers.get('content-type', '')
            content_length = len(response.content)
            
            print(f"✅ PDF generated successfully!")
            print(f"   Content-Type: {content_type}")
            print(f"   Content-Length: {content_length} bytes")
            
            # Check if it's actually PDF content
            if response.content.startswith(b'%PDF'):
                print("✅ Content appears to be valid PDF format")
                
                # Save the PDF to test file
                with open('test_report.pdf', 'wb') as f:
                    f.write(response.content)
                print("✅ PDF saved as 'test_report.pdf' for manual verification")
                
            else:
                print("❌ Content does not appear to be PDF format")
                print(f"   First 100 bytes: {response.content[:100]}")
                
        elif response.status_code == 401:
            print("❌ Authentication required - this is expected")
            print("   Need to test with proper authentication")
            
        else:
            print(f"❌ Unexpected response: {response.status_code}")
            print(f"   Response content: {response.text[:500]}")
            
    except Exception as e:
        print(f"❌ Error during PDF generation test: {str(e)}")
        return False
    
    return True

def test_with_mock_data():
    """Test PDF generation with mock data directly"""
    print("\n🧪 Testing PDF Generation with Mock Data")
    print("=" * 60)
    
    try:
        # Add backend to path
        sys.path.append('backend')
        
        from services.pdf_report_generator import PDFReportGenerator
        
        # Create mock report data
        mock_report = {
            'id': 999,
            'sid_number': 'MYD001',
            'billing_id': 999,
            'billing_date': '2025-06-20',
            'collection_date': '2025-06-20',
            'collection_time': '09:30 AM',
            'sample_id': 'MYD001',
            'patient_info': {
                'full_name': 'Prabagaran Kumar',
                'patient_id': 'P999',
                'date_of_birth': '1990-06-15',
                'age': '34',
                'gender': 'Male',
                'blood_group': 'O+',
                'mobile': '**********',
                'email': '<EMAIL>',
                'address': {
                    'street': '123 Medical Street',
                    'city': 'Chennai',
                    'state': 'Tamil Nadu',
                    'postal_code': '600001'
                }
            },
            'clinic_info': {
                'name': 'AVINI Labs Test',
                'address': 'Test Address',
                'contact_phone': '**********',
                'email': '<EMAIL>'
            },
            'billing_header': {
                'invoice_number': 'TEST001',
                'billing_period': '2025-06-20 to 2025-07-20',
                'referring_doctor': 'Dr. Test',
                'payment_status': 'Pending',
                'payment_method': 'Cash'
            },
            'test_items': [
                {
                    'test_name': 'Complete Blood Count (CBC)',
                    'department': 'HEMATOLOGY',
                    'hms_code': 'H001',
                    'reference_range': '4.5-11.0 x10³/µL',
                    'result_unit': 'x10³/µL',
                    'result_value': '7.2',
                    'method': 'Flow Cytometry',
                    'specimen': 'EDTA Blood',
                    'status': 'Completed'
                },
                {
                    'test_name': 'Fasting Blood Sugar',
                    'department': 'BIOCHEMISTRY',
                    'hms_code': 'B001',
                    'reference_range': '70-100 mg/dL',
                    'result_unit': 'mg/dL',
                    'result_value': '95',
                    'method': 'Enzymatic',
                    'specimen': 'Serum',
                    'status': 'Completed'
                },
                {
                    'test_name': 'Thyroid Stimulating Hormone (TSH)',
                    'department': 'IMMUNOLOGY',
                    'hms_code': 'I001',
                    'reference_range': '0.4-4.0 mIU/L',
                    'result_unit': 'mIU/L',
                    'result_value': '2.1',
                    'method': 'CLIA',
                    'specimen': 'Serum',
                    'status': 'Completed'
                }
            ],
            'financial_summary': {
                'bill_amount': 100,
                'other_charges': 0,
                'discount_percent': 0,
                'discount_amount': 0,
                'subtotal': 100,
                'gst_rate': 18,
                'gst_amount': 18,
                'total_amount': 118,
                'paid_amount': 0,
                'balance': 118
            },
            'metadata': {
                'total_tests': 1,
                'matched_tests_count': 1,
                'unmatched_tests_count': 0,
                'test_match_success_rate': 1.0
            }
        }
        
        # Generate PDF
        pdf_generator = PDFReportGenerator()
        pdf_content = pdf_generator.generate_comprehensive_billing_pdf(mock_report)
        
        print(f"✅ PDF generated successfully!")
        print(f"   Content type: {type(pdf_content)}")
        print(f"   Content length: {len(pdf_content)} bytes")
        
        # Check if it's PDF content
        if isinstance(pdf_content, bytes) and pdf_content.startswith(b'%PDF'):
            print("✅ Content appears to be valid PDF format")
            
            # Save the PDF
            with open('test_mock_report.pdf', 'wb') as f:
                f.write(pdf_content)
            print("✅ PDF saved as 'test_mock_report.pdf' for manual verification")
            
            return True
        else:
            print("❌ Content does not appear to be PDF format")
            if isinstance(pdf_content, bytes):
                print(f"   First 100 bytes: {pdf_content[:100]}")
            else:
                print(f"   Content: {str(pdf_content)[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ Error during mock PDF generation: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Starting PDF Generation Tests...")
    
    # Test 1: Direct mock data test
    success1 = test_with_mock_data()
    
    # Test 2: API endpoint test
    success2 = test_pdf_generation()
    
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Mock Data Test: {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"API Endpoint Test: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    if success1:
        print("\n🎉 PDF generation is working correctly!")
        print("You can now test the PDF download functionality in the web application.")
    else:
        print("\n❌ PDF generation needs to be fixed.")
