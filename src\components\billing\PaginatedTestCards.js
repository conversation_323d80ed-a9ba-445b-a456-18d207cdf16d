import React, { useState, useEffect } from 'react';
import { Row, Col, Alert } from 'react-bootstrap';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faInfoCircle } from '@fortawesome/free-solid-svg-icons';
import TestDetailsCard from './TestDetailsCard';
import Pagination from '../common/Pagination';

/**
 * PaginatedTestCards Component
 * Displays test items in a responsive card grid with pagination
 */
const PaginatedTestCards = ({ 
  testItems = [], 
  title = 'Test Details',
  itemsPerPage = 5 
}) => {
  const [currentPage, setCurrentPage] = useState(1);

  // Reset to first page when test items change
  useEffect(() => {
    setCurrentPage(1);
  }, [testItems]);

  // Calculate pagination
  const totalItems = testItems.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentItems = testItems.slice(startIndex, endIndex);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
    // Scroll to top of test cards section
    const testCardsSection = document.querySelector('.test-cards-section');
    if (testCardsSection) {
      testCardsSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  if (!testItems || testItems.length === 0) {
    return (
      <Alert variant="info">
        <div className="d-flex align-items-center">
          <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
          <div>
            <strong>No Test Details Available</strong>
            <div>No test items found for this billing report.</div>
          </div>
        </div>
      </Alert>
    );
  }

  return (
    <div className="test-cards-section">
      {/* Section Header */}
      <div className="d-flex justify-content-between align-items-center mb-3">
        <h6 className="text-primary mb-0">
          <FontAwesomeIcon icon={faInfoCircle} className="me-2" />
          {title} ({totalItems} tests)
        </h6>
        {totalPages > 1 && (
          <small className="text-muted">
            Page {currentPage} of {totalPages}
          </small>
        )}
      </div>

      {/* Test Cards Grid */}
      <Row className="test-cards-grid g-3 mb-4">
        {currentItems.map((test, index) => (
          <Col 
            key={test.id || test.test_master_id || test.test_id || index}
            xs={12}
            md={6}
            lg={4}
            className="test-card-column"
          >
            <TestDetailsCard 
              test={test} 
              index={startIndex + index}
            />
          </Col>
        ))}
      </Row>

      {/* Pagination Controls */}
      {totalPages > 1 && (
        <div className="test-cards-pagination">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            showInfo={true}
            size="sm"
            className="justify-content-center"
          />
        </div>
      )}

      {/* Mobile-specific pagination info */}
      <div className="d-block d-md-none text-center mt-3">
        <small className="text-muted">
          Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} tests
        </small>
      </div>
    </div>
  );
};

export default PaginatedTestCards;
